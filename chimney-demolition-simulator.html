<!DOCTYPE html>
<html lang="zh">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>砖块烟囱爆破模拟器</title>
    <style>
        body {
            margin: 0;
            padding: 0;
            overflow: hidden;
            background-color: #1a1a1a;
            font-family: Arial, sans-serif;
            color: white;
        }

        #canvas-container {
            position: relative;
            width: 100vw;
            height: 100vh;
        }

        #controls {
            position: absolute;
            top: 20px;
            left: 20px;
            background-color: rgba(0, 0, 0, 0.7);
            padding: 15px;
            border-radius: 10px;
            z-index: 10;
        }

        #controls button {
            display: block;
            width: 120px;
            padding: 10px;
            margin: 10px 0;
            background-color: #4CAF50;
            color: white;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-size: 14px;
        }

        #controls button:hover {
            background-color: #45a049;
        }

        #controls button:disabled {
            background-color: #666;
            cursor: not-allowed;
        }

        #info {
            position: absolute;
            bottom: 20px;
            left: 20px;
            background-color: rgba(0, 0, 0, 0.7);
            padding: 15px;
            border-radius: 10px;
            z-index: 10;
            font-size: 14px;
        }

        #instructions {
            margin-top: 15px;
            font-size: 12px;
            color: #ccc;
        }
    </style>
</head>
<body>
    <div id="canvas-container">
        <canvas id="mainCanvas"></canvas>
        
        <div id="controls">
            <button id="explodeBtn">引爆模拟</button>
            <button id="resetBtn">重置场景</button>
            <div id="instructions">
                鼠标左键：旋转视角<br>
                鼠标右键：移动位置<br>
                滚轮：缩放
            </div>
        </div>
        
        <div id="info">
            <div>物理对象数量: <span id="objectCount">0</span></div>
            <div>模拟状态: <span id="simulationStatus">等待引爆</span></div>
        </div>
    </div>

    <script>
        // 获取canvas元素和上下文
        const canvas = document.getElementById('mainCanvas');
        const ctx = canvas.getContext('2d');
        
        // 设置canvas尺寸
        function resizeCanvas() {
            canvas.width = window.innerWidth;
            canvas.height = window.innerHeight;
        }
        
        resizeCanvas();
        window.addEventListener('resize', resizeCanvas);
        
        // 相机设置
        const camera = {
            x: 0,
            y: 0,
            z: -500,
            rotX: 0,
            rotY: 0,
            zoom: 1
        };
        
        // 物理参数
        const physics = {
            gravity: 0.5,
            friction: 0.98,
            bounce: 0.7
        };
        
        // 模拟状态
        const simulation = {
            state: 'waiting', // 'waiting', 'exploding', 'finished'
            objects: [],
            ground: null
        };
        
        // 砖块类
        class Brick {
            constructor(x, y, z, width, height, depth, isStatic = false) {
                this.x = x;
                this.y = y;
                this.z = z;
                this.width = width;
                this.height = height;
                this.depth = depth;
                this.vx = 0;
                this.vy = 0;
                this.vz = 0;
                this.rotX = 0;
                this.rotY = 0;
                this.rotZ = 0;
                this.angularVx = 0;
                this.angularVy = 0;
                this.angularVz = 0;
                this.isStatic = isStatic;
                this.color = isStatic ? '#8B4513' : this.getRandomBrickColor();
                this.settled = false;
            }

            getRandomBrickColor() {
                const colors = ['#A0522D', '#CD853F', '#D2691E', '#B22222', '#8B4513'];
                return colors[Math.floor(Math.random() * colors.length)];
            }

            // 更新砖块位置
            update() {
                if (this.isStatic || this.settled) return;

                // 应用重力
                this.vy += physics.gravity;

                // 应用空气阻力
                this.vx *= physics.friction;
                this.vz *= physics.friction;
                this.vy *= 0.999; // 轻微的空气阻力

                // 更新角速度
                this.angularVx *= 0.98;
                this.angularVy *= 0.98;
                this.angularVz *= 0.98;

                // 更新位置
                this.x += this.vx;
                this.y += this.vy;
                this.z += this.vz;

                // 更新旋转
                this.rotX += this.angularVx;
                this.rotY += this.angularVy;
                this.rotZ += this.angularVz;

                // 地面碰撞检测
                if (this.y + this.height/2 > simulation.ground.y - simulation.ground.height/2) {
                    this.y = simulation.ground.y - simulation.ground.height/2 - this.height/2;
                    this.vy = -this.vy * physics.bounce;
                    this.vx *= 0.8; // 地面摩擦
                    this.vz *= 0.8;

                    // 添加随机旋转
                    this.angularVx += (Math.random() - 0.5) * 0.1;
                    this.angularVz += (Math.random() - 0.5) * 0.1;

                    // 如果速度很小，停止运动
                    if (Math.abs(this.vy) < 0.5 && Math.abs(this.vx) < 0.5 && Math.abs(this.vz) < 0.5) {
                        this.vy = 0;
                        this.vx = 0;
                        this.vz = 0;
                        this.settled = true;
                    }
                }

                // 边界检测
                const boundary = 1000;
                if (Math.abs(this.x) > boundary || Math.abs(this.z) > boundary) {
                    this.settled = true;
                }
            }

            // 绘制砖块
            draw() {
                // 3D到2D投影
                const scale = 800 / (800 + this.z + camera.z);
                const projX = (this.x + camera.x) * scale * camera.zoom + canvas.width/2;
                const projY = (this.y + camera.y) * scale * camera.zoom + canvas.height/2;
                const projWidth = this.width * scale * camera.zoom;
                const projHeight = this.height * scale * camera.zoom;

                // 保存上下文
                ctx.save();

                // 移动到砖块中心
                ctx.translate(projX, projY);

                // 应用旋转（简化的3D旋转效果）
                ctx.rotate(this.rotZ * 0.1);

                // 绘制砖块主体
                ctx.fillStyle = this.color;
                ctx.fillRect(-projWidth/2, -projHeight/2, projWidth, projHeight);

                // 添加3D效果的阴影
                const shadowOffset = 3 * scale * camera.zoom;
                ctx.fillStyle = 'rgba(0, 0, 0, 0.3)';
                ctx.fillRect(-projWidth/2 + shadowOffset, -projHeight/2 + shadowOffset, projWidth, projHeight);

                // 重新绘制主体
                ctx.fillStyle = this.color;
                ctx.fillRect(-projWidth/2, -projHeight/2, projWidth, projHeight);

                // 添加边框
                ctx.strokeStyle = '#000';
                ctx.lineWidth = 1;
                ctx.strokeRect(-projWidth/2, -projHeight/2, projWidth, projHeight);

                // 添加高光效果
                ctx.fillStyle = 'rgba(255, 255, 255, 0.2)';
                ctx.fillRect(-projWidth/2, -projHeight/2, projWidth/3, projHeight/3);

                // 恢复上下文
                ctx.restore();
            }
        }
        
        // 地面类
        class Ground {
            constructor(x, y, z, width, height, depth) {
                this.x = x;
                this.y = y;
                this.z = z;
                this.width = width;
                this.height = height;
                this.depth = depth;
                this.color = '#444';
            }

            update() {
                // 地面不需要更新
            }

            // 绘制地面
            draw() {
                // 3D到2D投影
                const scale = 800 / (800 + this.z + camera.z);
                const projX = (this.x + camera.x) * scale * camera.zoom + canvas.width/2;
                const projY = (this.y + camera.y) * scale * camera.zoom + canvas.height/2;
                const projWidth = this.width * scale * camera.zoom;
                const projHeight = this.height * scale * camera.zoom;

                // 绘制地面主体
                ctx.fillStyle = this.color;
                ctx.fillRect(projX - projWidth/2, projY - projHeight/2, projWidth, projHeight);

                // 添加网格线
                ctx.strokeStyle = '#666';
                ctx.lineWidth = 1;
                const gridSize = 50 * scale * camera.zoom;

                // 垂直线
                for (let i = -10; i <= 10; i++) {
                    const lineX = projX + i * gridSize;
                    if (lineX >= projX - projWidth/2 && lineX <= projX + projWidth/2) {
                        ctx.beginPath();
                        ctx.moveTo(lineX, projY - projHeight/2);
                        ctx.lineTo(lineX, projY + projHeight/2);
                        ctx.stroke();
                    }
                }

                // 水平线
                for (let i = -10; i <= 10; i++) {
                    const lineY = projY + i * gridSize;
                    if (lineY >= projY - projHeight/2 && lineY <= projY + projHeight/2) {
                        ctx.beginPath();
                        ctx.moveTo(projX - projWidth/2, lineY);
                        ctx.lineTo(projX + projWidth/2, lineY);
                        ctx.stroke();
                    }
                }

                // 添加边框
                ctx.strokeStyle = '#222';
                ctx.lineWidth = 2;
                ctx.strokeRect(projX - projWidth/2, projY - projHeight/2, projWidth, projHeight);
            }
        }
        
        // 创建烟囱
        function createChimney() {
            simulation.objects = [];

            // 创建地面
            simulation.ground = new Ground(0, 200, 0, 2000, 20, 2000);
            simulation.objects.push(simulation.ground);

            // 烟囱参数
            const brickWidth = 35;
            const brickHeight = 18;
            const brickDepth = 20;
            const chimneyHeight = 20; // 砖块层数
            const chimneyWidth = 6;   // 每层砖块数

            // 创建烟囱砖块 - 使用交错排列增加稳定性
            for (let layer = 0; layer < chimneyHeight; layer++) {
                const layerY = -layer * brickHeight - brickHeight/2;
                const isEvenLayer = layer % 2 === 0;

                // 每层砖块数量随高度递减，形成锥形
                const currentWidth = Math.max(3, chimneyWidth - Math.floor(layer / 4));

                for (let i = 0; i < currentWidth; i++) {
                    // 交错排列
                    const offset = isEvenLayer ? 0 : brickWidth * 0.5;
                    const brickX = (i - (currentWidth-1)/2) * brickWidth + offset;

                    // 添加轻微的随机偏移，使结构更自然
                    const randomOffsetX = (Math.random() - 0.5) * 2;
                    const randomOffsetZ = (Math.random() - 0.5) * 2;

                    const brick = new Brick(
                        brickX + randomOffsetX,
                        layerY,
                        randomOffsetZ,
                        brickWidth,
                        brickHeight,
                        brickDepth,
                        false
                    );

                    simulation.objects.push(brick);
                }

                // 在某些层添加横向砖块增加稳定性
                if (layer % 3 === 0 && layer > 0) {
                    for (let j = 0; j < currentWidth - 1; j++) {
                        const crossBrickX = (j - (currentWidth-2)/2) * brickWidth + brickWidth * 0.5;
                        const crossBrick = new Brick(
                            crossBrickX,
                            layerY - brickHeight * 0.5,
                            brickDepth * 0.7,
                            brickWidth * 0.8,
                            brickHeight * 0.8,
                            brickDepth,
                            false
                        );
                        simulation.objects.push(crossBrick);
                    }
                }
            }

            updateObjectCount();
        }
        
        // 引爆烟囱
        function explodeChimney() {
            if (simulation.state !== 'waiting') return;

            simulation.state = 'exploding';
            document.getElementById('simulationStatus').textContent = '模拟中';
            document.getElementById('explodeBtn').disabled = true;

            // 分层爆破效果
            const bricks = simulation.objects.filter(obj => obj instanceof Brick && !obj.isStatic);

            bricks.forEach((brick, index) => {
                setTimeout(() => {
                    // 计算距离爆破中心的距离
                    const distanceFromCenter = Math.sqrt(brick.x * brick.x + brick.z * brick.z);
                    const heightFactor = Math.abs(brick.y) / 300; // 高度因子

                    // 爆破力度随距离和高度变化
                    const explosionForce = 15 - distanceFromCenter * 0.01 + heightFactor * 5;

                    // 径向爆破方向
                    const angle = Math.atan2(brick.z, brick.x);
                    const radialForce = explosionForce * (0.5 + Math.random() * 0.5);

                    // 设置速度
                    brick.vx = Math.cos(angle) * radialForce + (Math.random() - 0.5) * 5;
                    brick.vy = -(Math.random() * 8 + 3) - heightFactor * 3;
                    brick.vz = Math.sin(angle) * radialForce + (Math.random() - 0.5) * 5;

                    // 添加旋转
                    brick.angularVx = (Math.random() - 0.5) * 0.3;
                    brick.angularVy = (Math.random() - 0.5) * 0.3;
                    brick.angularVz = (Math.random() - 0.5) * 0.3;

                    // 重置settled状态
                    brick.settled = false;
                }, Math.random() * 200); // 随机延迟，模拟连锁爆破
            });
        }
        
        // 重置场景
        function resetScene() {
            simulation.state = 'waiting';
            document.getElementById('simulationStatus').textContent = '等待引爆';
            document.getElementById('explodeBtn').disabled = false;
            createChimney();
        }
        
        // 更新对象计数
        function updateObjectCount() {
            const brickCount = simulation.objects.filter(obj => obj instanceof Brick && !obj.isStatic).length;
            document.getElementById('objectCount').textContent = brickCount;
        }
        
        // 碰撞检测
        function detectCollisions() {
            const bricks = simulation.objects.filter(obj => obj instanceof Brick && !obj.isStatic && !obj.settled);

            // 砖块之间的碰撞检测
            for (let i = 0; i < bricks.length; i++) {
                for (let j = i + 1; j < bricks.length; j++) {
                    const brick1 = bricks[i];
                    const brick2 = bricks[j];

                    // 简单的AABB碰撞检测
                    const dx = Math.abs(brick1.x - brick2.x);
                    const dy = Math.abs(brick1.y - brick2.y);
                    const dz = Math.abs(brick1.z - brick2.z);

                    const minDistX = (brick1.width + brick2.width) / 2;
                    const minDistY = (brick1.height + brick2.height) / 2;
                    const minDistZ = (brick1.depth + brick2.depth) / 2;

                    if (dx < minDistX && dy < minDistY && dz < minDistZ) {
                        // 发生碰撞，计算反弹
                        const overlapX = minDistX - dx;
                        const overlapY = minDistY - dy;
                        const overlapZ = minDistZ - dz;

                        // 找到最小重叠轴
                        let separationAxis;
                        let separation;

                        if (overlapX <= overlapY && overlapX <= overlapZ) {
                            separationAxis = 'x';
                            separation = overlapX;
                        } else if (overlapY <= overlapZ) {
                            separationAxis = 'y';
                            separation = overlapY;
                        } else {
                            separationAxis = 'z';
                            separation = overlapZ;
                        }

                        // 分离砖块
                        const direction = separationAxis === 'x' ? (brick1.x > brick2.x ? 1 : -1) :
                                        separationAxis === 'y' ? (brick1.y > brick2.y ? 1 : -1) :
                                        (brick1.z > brick2.z ? 1 : -1);

                        if (separationAxis === 'x') {
                            brick1.x += direction * separation * 0.5;
                            brick2.x -= direction * separation * 0.5;

                            // 交换速度分量
                            const tempVx = brick1.vx;
                            brick1.vx = brick2.vx * 0.8;
                            brick2.vx = tempVx * 0.8;
                        } else if (separationAxis === 'y') {
                            brick1.y += direction * separation * 0.5;
                            brick2.y -= direction * separation * 0.5;

                            const tempVy = brick1.vy;
                            brick1.vy = brick2.vy * 0.8;
                            brick2.vy = tempVy * 0.8;
                        } else {
                            brick1.z += direction * separation * 0.5;
                            brick2.z -= direction * separation * 0.5;

                            const tempVz = brick1.vz;
                            brick1.vz = brick2.vz * 0.8;
                            brick2.vz = tempVz * 0.8;
                        }

                        // 添加随机旋转
                        brick1.angularVx += (Math.random() - 0.5) * 0.1;
                        brick1.angularVy += (Math.random() - 0.5) * 0.1;
                        brick1.angularVz += (Math.random() - 0.5) * 0.1;

                        brick2.angularVx += (Math.random() - 0.5) * 0.1;
                        brick2.angularVy += (Math.random() - 0.5) * 0.1;
                        brick2.angularVz += (Math.random() - 0.5) * 0.1;
                    }
                }
            }
        }
        
        // 渲染场景
        function render() {
            // 清空画布
            ctx.fillStyle = '#1a1a1a';
            ctx.fillRect(0, 0, canvas.width, canvas.height);
            
            // 按照z深度排序对象（简单的画家算法）
            const sortedObjects = [...simulation.objects].sort((a, b) => {
                return (b.z - a.z);
            });
            
            // 绘制所有对象
            for (const obj of sortedObjects) {
                obj.draw();
            }
        }
        
        // 更新模拟
        function update() {
            if (simulation.state === 'exploding') {
                let movingCount = 0;
                let settledCount = 0;

                // 更新所有对象
                for (const obj of simulation.objects) {
                    if (obj.update) {
                        obj.update();
                    }

                    if (obj instanceof Brick && !obj.isStatic) {
                        if (obj.settled) {
                            settledCount++;
                        } else if (Math.abs(obj.vx) > 0.1 || Math.abs(obj.vy) > 0.1 || Math.abs(obj.vz) > 0.1) {
                            movingCount++;
                        }
                    }
                }

                // 检测碰撞（只在有移动物体时）
                if (movingCount > 0) {
                    detectCollisions();
                }

                // 更新对象计数
                const totalBricks = simulation.objects.filter(obj => obj instanceof Brick && !obj.isStatic).length;
                document.getElementById('objectCount').textContent = `${totalBricks} (移动: ${movingCount}, 静止: ${settledCount})`;

                // 如果大部分砖块都静止了，模拟结束
                if (movingCount === 0 && settledCount > totalBricks * 0.8) {
                    simulation.state = 'finished';
                    document.getElementById('simulationStatus').textContent = '模拟完成';
                }
            }
        }
        
        // 主循环
        function mainLoop() {
            update();
            render();
            requestAnimationFrame(mainLoop);
        }
        
        // 鼠标控制
        let isMouseDown = false;
        let lastMouseX = 0;
        let lastMouseY = 0;
        let mouseButton = 0;
        
        canvas.addEventListener('mousedown', (e) => {
            isMouseDown = true;
            mouseButton = e.button;
            lastMouseX = e.clientX;
            lastMouseY = e.clientY;
        });
        
        canvas.addEventListener('mouseup', () => {
            isMouseDown = false;
        });
        
        canvas.addEventListener('mousemove', (e) => {
            if (!isMouseDown) return;
            
            const deltaX = e.clientX - lastMouseX;
            const deltaY = e.clientY - lastMouseY;
            
            if (mouseButton === 0) { // 左键 - 旋转视角
                camera.rotY += deltaX * 0.01;
                camera.rotX += deltaY * 0.01;
            } else if (mouseButton === 2) { // 右键 - 移动位置
                camera.x += deltaX * 2;
                camera.y += deltaY * 2;
            }
            
            lastMouseX = e.clientX;
            lastMouseY = e.clientY;
        });
        
        canvas.addEventListener('wheel', (e) => {
            e.preventDefault();
            camera.zoom += e.deltaY * -0.001;
            
            // 限制缩放范围
            camera.zoom = Math.min(Math.max(0.5, camera.zoom), 3);
        });
        
        // 阻止右键菜单
        canvas.addEventListener('contextmenu', (e) => {
            e.preventDefault();
        });
        
        // 按钮事件
        document.getElementById('explodeBtn').addEventListener('click', explodeChimney);
        document.getElementById('resetBtn').addEventListener('click', resetScene);
        
        // 初始化场景
        createChimney();
        mainLoop();
    </script>
</body>
</html>
