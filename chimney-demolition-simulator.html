<!DOCTYPE html>
<html lang="zh">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>砖块烟囱爆破模拟器</title>
    <style>
        body {
            margin: 0;
            padding: 0;
            overflow: hidden;
            background-color: #1a1a1a;
            font-family: Arial, sans-serif;
            color: white;
        }

        #canvas-container {
            position: relative;
            width: 100vw;
            height: 100vh;
        }

        #controls {
            position: absolute;
            top: 20px;
            left: 20px;
            background-color: rgba(0, 0, 0, 0.7);
            padding: 15px;
            border-radius: 10px;
            z-index: 10;
        }

        #controls button {
            display: block;
            width: 120px;
            padding: 10px;
            margin: 10px 0;
            background-color: #4CAF50;
            color: white;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-size: 14px;
        }

        #controls button:hover {
            background-color: #45a049;
        }

        #controls button:disabled {
            background-color: #666;
            cursor: not-allowed;
        }

        #info {
            position: absolute;
            bottom: 20px;
            left: 20px;
            background-color: rgba(0, 0, 0, 0.7);
            padding: 15px;
            border-radius: 10px;
            z-index: 10;
            font-size: 14px;
        }

        #instructions {
            margin-top: 15px;
            font-size: 12px;
            color: #ccc;
        }
    </style>
</head>
<body>
    <div id="canvas-container">
        <canvas id="mainCanvas"></canvas>
        
        <div id="controls">
            <button id="explodeBtn">引爆模拟</button>
            <button id="resetBtn">重置场景</button>
            <div id="instructions">
                鼠标左键：旋转视角<br>
                鼠标右键：移动位置<br>
                滚轮：缩放
            </div>
        </div>
        
        <div id="info">
            <div>物理对象数量: <span id="objectCount">0</span></div>
            <div>模拟状态: <span id="simulationStatus">等待引爆</span></div>
        </div>
    </div>

    <script>
        // 获取canvas元素和上下文
        const canvas = document.getElementById('mainCanvas');
        const ctx = canvas.getContext('2d');
        
        // 设置canvas尺寸
        function resizeCanvas() {
            canvas.width = window.innerWidth;
            canvas.height = window.innerHeight;
        }
        
        resizeCanvas();
        window.addEventListener('resize', resizeCanvas);
        
        // 相机设置
        const camera = {
            x: 0,
            y: -50,
            z: -400,
            rotX: 0.2,
            rotY: 0.3,
            zoom: 1.2
        };
        
        // 物理参数
        const physics = {
            gravity: 0.3,
            friction: 0.995,
            bounce: 0.4,
            airResistance: 0.999
        };
        
        // 模拟状态
        const simulation = {
            state: 'waiting', // 'waiting', 'exploding', 'finished'
            objects: [],
            ground: null
        };
        
        // 砖块类
        class Brick {
            constructor(x, y, z, width, height, depth, isStatic = false) {
                this.x = x;
                this.y = y;
                this.z = z;
                this.width = width;
                this.height = height;
                this.depth = depth;
                this.vx = 0;
                this.vy = 0;
                this.vz = 0;
                this.rotX = 0;
                this.rotY = 0;
                this.rotZ = 0;
                this.angularVx = 0;
                this.angularVy = 0;
                this.angularVz = 0;
                this.isStatic = isStatic;
                this.color = isStatic ? '#8B4513' : this.getRandomBrickColor();
                this.settled = false;
            }

            getRandomBrickColor() {
                const colors = ['#A0522D', '#CD853F', '#D2691E', '#B22222', '#8B4513'];
                return colors[Math.floor(Math.random() * colors.length)];
            }

            // 更新砖块位置
            update() {
                if (this.isStatic || this.settled) return;

                // 应用重力
                this.vy += physics.gravity;

                // 应用空气阻力
                this.vx *= physics.airResistance;
                this.vz *= physics.airResistance;
                this.vy *= physics.airResistance;

                // 更新角速度
                this.angularVx *= 0.99;
                this.angularVy *= 0.99;
                this.angularVz *= 0.99;

                // 更新位置
                this.x += this.vx;
                this.y += this.vy;
                this.z += this.vz;

                // 更新旋转
                this.rotX += this.angularVx;
                this.rotY += this.angularVy;
                this.rotZ += this.angularVz;

                // 地面碰撞检测
                if (this.y + this.height/2 > simulation.ground.y - simulation.ground.height/2) {
                    this.y = simulation.ground.y - simulation.ground.height/2 - this.height/2;
                    this.vy = -this.vy * physics.bounce;
                    this.vx *= physics.friction; // 地面摩擦
                    this.vz *= physics.friction;

                    // 添加随机旋转
                    this.angularVx += (Math.random() - 0.5) * 0.05;
                    this.angularVz += (Math.random() - 0.5) * 0.05;

                    // 如果速度很小，停止运动
                    if (Math.abs(this.vy) < 0.3 && Math.abs(this.vx) < 0.3 && Math.abs(this.vz) < 0.3) {
                        this.vy = 0;
                        this.vx *= 0.9;
                        this.vz *= 0.9;
                        if (Math.abs(this.vx) < 0.1 && Math.abs(this.vz) < 0.1) {
                            this.vx = 0;
                            this.vz = 0;
                            this.settled = true;
                        }
                    }
                }

                // 边界检测
                const boundary = 800;
                if (Math.abs(this.x) > boundary || Math.abs(this.z) > boundary) {
                    this.settled = true;
                }
            }

            // 绘制砖块
            draw() {
                // 改进的3D到2D投影
                const distance = 600;
                const scale = distance / (distance + this.z + camera.z);

                // 应用相机旋转
                const cosY = Math.cos(camera.rotY);
                const sinY = Math.sin(camera.rotY);
                const cosX = Math.cos(camera.rotX);
                const sinX = Math.sin(camera.rotX);

                // 3D变换
                let x = this.x - camera.x;
                let y = this.y - camera.y;
                let z = this.z - camera.z;

                // Y轴旋转
                let newX = x * cosY - z * sinY;
                let newZ = x * sinY + z * cosY;
                x = newX;
                z = newZ;

                // X轴旋转
                let newY = y * cosX - z * sinX;
                newZ = y * sinX + z * cosX;
                y = newY;
                z = newZ;

                // 投影到屏幕
                const projX = (x * distance / (distance + z)) * camera.zoom + canvas.width/2;
                const projY = (y * distance / (distance + z)) * camera.zoom + canvas.height/2;
                const projWidth = this.width * distance / (distance + z) * camera.zoom;
                const projHeight = this.height * distance / (distance + z) * camera.zoom;

                // 如果在屏幕后面，不绘制
                if (z < -distance) return;

                // 保存上下文
                ctx.save();

                // 移动到砖块中心
                ctx.translate(projX, projY);

                // 应用旋转
                ctx.rotate(this.rotZ * 0.3 + this.rotY * 0.2);

                // 计算光照效果
                const lightIntensity = Math.max(0.3, 1 - z / 1000);

                // 绘制3D砖块效果
                const depth = Math.max(2, projWidth * 0.1);

                // 绘制右侧面（深色）
                ctx.fillStyle = this.adjustBrightness(this.color, 0.6 * lightIntensity);
                ctx.fillRect(projWidth/2, -projHeight/2, depth, projHeight);
                ctx.fillRect(projWidth/2, projHeight/2, -projWidth, depth);

                // 绘制主面
                ctx.fillStyle = this.adjustBrightness(this.color, lightIntensity);
                ctx.fillRect(-projWidth/2, -projHeight/2, projWidth, projHeight);

                // 添加边框
                ctx.strokeStyle = '#000';
                ctx.lineWidth = Math.max(0.5, scale);
                ctx.strokeRect(-projWidth/2, -projHeight/2, projWidth, projHeight);

                // 添加高光效果
                ctx.fillStyle = `rgba(255, 255, 255, ${0.15 * lightIntensity})`;
                ctx.fillRect(-projWidth/2, -projHeight/2, projWidth/4, projHeight/4);

                // 恢复上下文
                ctx.restore();
            }

            // 调整颜色亮度
            adjustBrightness(color, factor) {
                const hex = color.replace('#', '');
                const r = Math.floor(parseInt(hex.substr(0, 2), 16) * factor);
                const g = Math.floor(parseInt(hex.substr(2, 2), 16) * factor);
                const b = Math.floor(parseInt(hex.substr(4, 2), 16) * factor);
                return `rgb(${r}, ${g}, ${b})`;
            }
        }
        
        // 地面类
        class Ground {
            constructor(x, y, z, width, height, depth) {
                this.x = x;
                this.y = y;
                this.z = z;
                this.width = width;
                this.height = height;
                this.depth = depth;
                this.color = '#444';
            }

            update() {
                // 地面不需要更新
            }

            // 绘制地面
            draw() {
                // 3D到2D投影
                const scale = 800 / (800 + this.z + camera.z);
                const projX = (this.x + camera.x) * scale * camera.zoom + canvas.width/2;
                const projY = (this.y + camera.y) * scale * camera.zoom + canvas.height/2;
                const projWidth = this.width * scale * camera.zoom;
                const projHeight = this.height * scale * camera.zoom;

                // 绘制地面主体
                ctx.fillStyle = this.color;
                ctx.fillRect(projX - projWidth/2, projY - projHeight/2, projWidth, projHeight);

                // 添加网格线
                ctx.strokeStyle = '#666';
                ctx.lineWidth = 1;
                const gridSize = 50 * scale * camera.zoom;

                // 垂直线
                for (let i = -10; i <= 10; i++) {
                    const lineX = projX + i * gridSize;
                    if (lineX >= projX - projWidth/2 && lineX <= projX + projWidth/2) {
                        ctx.beginPath();
                        ctx.moveTo(lineX, projY - projHeight/2);
                        ctx.lineTo(lineX, projY + projHeight/2);
                        ctx.stroke();
                    }
                }

                // 水平线
                for (let i = -10; i <= 10; i++) {
                    const lineY = projY + i * gridSize;
                    if (lineY >= projY - projHeight/2 && lineY <= projY + projHeight/2) {
                        ctx.beginPath();
                        ctx.moveTo(projX - projWidth/2, lineY);
                        ctx.lineTo(projX + projWidth/2, lineY);
                        ctx.stroke();
                    }
                }

                // 添加边框
                ctx.strokeStyle = '#222';
                ctx.lineWidth = 2;
                ctx.strokeRect(projX - projWidth/2, projY - projHeight/2, projWidth, projHeight);
            }
        }
        
        // 创建烟囱
        function createChimney() {
            simulation.objects = [];

            // 创建地面
            simulation.ground = new Ground(0, 180, 0, 1500, 20, 1500);
            simulation.objects.push(simulation.ground);

            // 烟囱参数
            const brickWidth = 40;
            const brickHeight = 20;
            const brickDepth = 25;
            const chimneyHeight = 18; // 砖块层数
            const baseWidth = 7;      // 底层砖块数

            // 创建烟囱砖块 - 圆形排列
            for (let layer = 0; layer < chimneyHeight; layer++) {
                const layerY = -layer * brickHeight - brickHeight/2;

                // 每层砖块数量随高度递减
                const currentWidth = Math.max(4, baseWidth - Math.floor(layer / 3));
                const radius = currentWidth * brickWidth / (2 * Math.PI);

                // 交错排列
                const angleOffset = (layer % 2) * (Math.PI / currentWidth);

                for (let i = 0; i < currentWidth; i++) {
                    const angle = (i / currentWidth) * Math.PI * 2 + angleOffset;
                    const brickX = Math.cos(angle) * radius;
                    const brickZ = Math.sin(angle) * radius;

                    // 添加轻微的随机偏移
                    const randomOffsetX = (Math.random() - 0.5) * 3;
                    const randomOffsetZ = (Math.random() - 0.5) * 3;
                    const randomOffsetY = (Math.random() - 0.5) * 2;

                    const brick = new Brick(
                        brickX + randomOffsetX,
                        layerY + randomOffsetY,
                        brickZ + randomOffsetZ,
                        brickWidth,
                        brickHeight,
                        brickDepth,
                        false
                    );

                    // 设置砖块朝向中心的旋转
                    brick.rotY = angle + Math.PI/2;

                    simulation.objects.push(brick);
                }

                // 在某些层添加内部支撑砖块
                if (layer % 4 === 0 && layer > 0 && currentWidth > 4) {
                    const innerRadius = radius * 0.6;
                    const innerBricks = Math.max(2, Math.floor(currentWidth * 0.6));

                    for (let j = 0; j < innerBricks; j++) {
                        const angle = (j / innerBricks) * Math.PI * 2;
                        const brickX = Math.cos(angle) * innerRadius;
                        const brickZ = Math.sin(angle) * innerRadius;

                        const innerBrick = new Brick(
                            brickX,
                            layerY - brickHeight * 0.3,
                            brickZ,
                            brickWidth * 0.8,
                            brickHeight * 0.7,
                            brickDepth * 0.8,
                            false
                        );

                        simulation.objects.push(innerBrick);
                    }
                }
            }

            updateObjectCount();
        }
        
        // 引爆烟囱
        function explodeChimney() {
            if (simulation.state !== 'waiting') return;

            simulation.state = 'exploding';
            document.getElementById('simulationStatus').textContent = '模拟中';
            document.getElementById('explodeBtn').disabled = true;

            // 分层爆破效果
            const bricks = simulation.objects.filter(obj => obj instanceof Brick && !obj.isStatic);

            // 从底部开始爆破
            bricks.sort((a, b) => b.y - a.y); // 按高度排序，底部优先

            bricks.forEach((brick, index) => {
                const delay = Math.floor(index / 6) * 50 + Math.random() * 100; // 分层延迟

                setTimeout(() => {
                    // 计算爆破参数
                    const distanceFromCenter = Math.sqrt(brick.x * brick.x + brick.z * brick.z);
                    const heightFactor = Math.max(0, -brick.y / 200); // 高度因子
                    const layerIndex = Math.floor(index / 6);

                    // 爆破力度
                    const baseForce = 12 + heightFactor * 8;
                    const randomFactor = 0.7 + Math.random() * 0.6;
                    const explosionForce = baseForce * randomFactor;

                    // 径向爆破方向
                    let angle = Math.atan2(brick.z, brick.x);
                    if (Math.abs(brick.x) < 10 && Math.abs(brick.z) < 10) {
                        // 如果在中心，随机方向
                        angle = Math.random() * Math.PI * 2;
                    }

                    // 添加一些向上的力
                    const upwardForce = 3 + heightFactor * 4 + Math.random() * 3;

                    // 设置速度
                    brick.vx = Math.cos(angle) * explosionForce + (Math.random() - 0.5) * 4;
                    brick.vy = -upwardForce - Math.random() * 2;
                    brick.vz = Math.sin(angle) * explosionForce + (Math.random() - 0.5) * 4;

                    // 添加强烈的旋转
                    brick.angularVx = (Math.random() - 0.5) * 0.4;
                    brick.angularVy = (Math.random() - 0.5) * 0.4;
                    brick.angularVz = (Math.random() - 0.5) * 0.4;

                    // 重置settled状态
                    brick.settled = false;
                }, delay);
            });

            // 添加相机震动效果
            let shakeIntensity = 10;
            const originalCameraX = camera.x;
            const originalCameraY = camera.y;

            const shakeInterval = setInterval(() => {
                camera.x = originalCameraX + (Math.random() - 0.5) * shakeIntensity;
                camera.y = originalCameraY + (Math.random() - 0.5) * shakeIntensity;
                shakeIntensity *= 0.9;

                if (shakeIntensity < 0.5) {
                    clearInterval(shakeInterval);
                    camera.x = originalCameraX;
                    camera.y = originalCameraY;
                }
            }, 50);
        }
        
        // 重置场景
        function resetScene() {
            simulation.state = 'waiting';
            document.getElementById('simulationStatus').textContent = '等待引爆';
            document.getElementById('explodeBtn').disabled = false;
            createChimney();
        }
        
        // 更新对象计数
        function updateObjectCount() {
            const brickCount = simulation.objects.filter(obj => obj instanceof Brick && !obj.isStatic).length;
            document.getElementById('objectCount').textContent = brickCount;
        }
        
        // 碰撞检测
        function detectCollisions() {
            const bricks = simulation.objects.filter(obj => obj instanceof Brick && !obj.isStatic && !obj.settled);

            // 砖块之间的碰撞检测
            for (let i = 0; i < bricks.length; i++) {
                for (let j = i + 1; j < bricks.length; j++) {
                    const brick1 = bricks[i];
                    const brick2 = bricks[j];

                    // 简单的AABB碰撞检测
                    const dx = Math.abs(brick1.x - brick2.x);
                    const dy = Math.abs(brick1.y - brick2.y);
                    const dz = Math.abs(brick1.z - brick2.z);

                    const minDistX = (brick1.width + brick2.width) / 2;
                    const minDistY = (brick1.height + brick2.height) / 2;
                    const minDistZ = (brick1.depth + brick2.depth) / 2;

                    if (dx < minDistX && dy < minDistY && dz < minDistZ) {
                        // 发生碰撞，计算反弹
                        const overlapX = minDistX - dx;
                        const overlapY = minDistY - dy;
                        const overlapZ = minDistZ - dz;

                        // 找到最小重叠轴
                        let separationAxis;
                        let separation;

                        if (overlapX <= overlapY && overlapX <= overlapZ) {
                            separationAxis = 'x';
                            separation = overlapX;
                        } else if (overlapY <= overlapZ) {
                            separationAxis = 'y';
                            separation = overlapY;
                        } else {
                            separationAxis = 'z';
                            separation = overlapZ;
                        }

                        // 分离砖块
                        const direction = separationAxis === 'x' ? (brick1.x > brick2.x ? 1 : -1) :
                                        separationAxis === 'y' ? (brick1.y > brick2.y ? 1 : -1) :
                                        (brick1.z > brick2.z ? 1 : -1);

                        if (separationAxis === 'x') {
                            brick1.x += direction * separation * 0.5;
                            brick2.x -= direction * separation * 0.5;

                            // 交换速度分量
                            const tempVx = brick1.vx;
                            brick1.vx = brick2.vx * 0.8;
                            brick2.vx = tempVx * 0.8;
                        } else if (separationAxis === 'y') {
                            brick1.y += direction * separation * 0.5;
                            brick2.y -= direction * separation * 0.5;

                            const tempVy = brick1.vy;
                            brick1.vy = brick2.vy * 0.8;
                            brick2.vy = tempVy * 0.8;
                        } else {
                            brick1.z += direction * separation * 0.5;
                            brick2.z -= direction * separation * 0.5;

                            const tempVz = brick1.vz;
                            brick1.vz = brick2.vz * 0.8;
                            brick2.vz = tempVz * 0.8;
                        }

                        // 添加随机旋转
                        brick1.angularVx += (Math.random() - 0.5) * 0.1;
                        brick1.angularVy += (Math.random() - 0.5) * 0.1;
                        brick1.angularVz += (Math.random() - 0.5) * 0.1;

                        brick2.angularVx += (Math.random() - 0.5) * 0.1;
                        brick2.angularVy += (Math.random() - 0.5) * 0.1;
                        brick2.angularVz += (Math.random() - 0.5) * 0.1;
                    }
                }
            }
        }
        
        // 渲染场景
        function render() {
            // 清空画布
            ctx.fillStyle = '#1a1a1a';
            ctx.fillRect(0, 0, canvas.width, canvas.height);
            
            // 按照z深度排序对象（简单的画家算法）
            const sortedObjects = [...simulation.objects].sort((a, b) => {
                return (b.z - a.z);
            });
            
            // 绘制所有对象
            for (const obj of sortedObjects) {
                obj.draw();
            }
        }
        
        // 更新模拟
        function update() {
            if (simulation.state === 'exploding') {
                let movingCount = 0;
                let settledCount = 0;

                // 更新所有对象
                for (const obj of simulation.objects) {
                    if (obj.update) {
                        obj.update();
                    }

                    if (obj instanceof Brick && !obj.isStatic) {
                        if (obj.settled) {
                            settledCount++;
                        } else if (Math.abs(obj.vx) > 0.1 || Math.abs(obj.vy) > 0.1 || Math.abs(obj.vz) > 0.1) {
                            movingCount++;
                        }
                    }
                }

                // 检测碰撞（只在有移动物体时）
                if (movingCount > 0) {
                    detectCollisions();
                }

                // 更新对象计数
                const totalBricks = simulation.objects.filter(obj => obj instanceof Brick && !obj.isStatic).length;
                document.getElementById('objectCount').textContent = `${totalBricks} (移动: ${movingCount}, 静止: ${settledCount})`;

                // 如果大部分砖块都静止了，模拟结束
                if (movingCount === 0 && settledCount > totalBricks * 0.8) {
                    simulation.state = 'finished';
                    document.getElementById('simulationStatus').textContent = '模拟完成';
                }
            }
        }
        
        // 主循环
        function mainLoop() {
            update();
            render();
            requestAnimationFrame(mainLoop);
        }
        
        // 鼠标控制
        let isMouseDown = false;
        let lastMouseX = 0;
        let lastMouseY = 0;
        let mouseButton = 0;
        
        canvas.addEventListener('mousedown', (e) => {
            isMouseDown = true;
            mouseButton = e.button;
            lastMouseX = e.clientX;
            lastMouseY = e.clientY;
        });
        
        canvas.addEventListener('mouseup', () => {
            isMouseDown = false;
        });
        
        canvas.addEventListener('mousemove', (e) => {
            if (!isMouseDown) return;
            
            const deltaX = e.clientX - lastMouseX;
            const deltaY = e.clientY - lastMouseY;
            
            if (mouseButton === 0) { // 左键 - 旋转视角
                camera.rotY += deltaX * 0.01;
                camera.rotX += deltaY * 0.01;
            } else if (mouseButton === 2) { // 右键 - 移动位置
                camera.x += deltaX * 2;
                camera.y += deltaY * 2;
            }
            
            lastMouseX = e.clientX;
            lastMouseY = e.clientY;
        });
        
        canvas.addEventListener('wheel', (e) => {
            e.preventDefault();
            camera.zoom += e.deltaY * -0.001;
            
            // 限制缩放范围
            camera.zoom = Math.min(Math.max(0.5, camera.zoom), 3);
        });
        
        // 阻止右键菜单
        canvas.addEventListener('contextmenu', (e) => {
            e.preventDefault();
        });
        
        // 按钮事件
        document.getElementById('explodeBtn').addEventListener('click', explodeChimney);
        document.getElementById('resetBtn').addEventListener('click', resetScene);
        
        // 初始化场景
        createChimney();
        mainLoop();
    </script>
</body>
</html>
