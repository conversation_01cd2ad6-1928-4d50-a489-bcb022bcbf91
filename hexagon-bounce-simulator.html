<!DOCTYPE html>
<html lang="zh">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>六边形弹球模拟器</title>
    <style>
        body {
            margin: 0;
            padding: 0;
            background-color: #121212;
            color: #e0e0e0;
            font-family: Arial, sans-serif;
            display: flex;
            flex-direction: row-reverse;
            min-height: 100vh;
        }

        .control-panel {
            width: 300px;
            padding: 20px;
            background-color: #1e1e1e;
            box-shadow: -2px 0 10px rgba(0, 0, 0, 0.5);
            z-index: 10;
        }

        .control-panel h1 {
            text-align: center;
            font-size: 1.5em;
            margin-top: 0;
            color: #64b5f6;
        }

        .control-group {
            margin-bottom: 20px;
        }

        .control-group label {
            display: block;
            margin-bottom: 5px;
        }

        .control-group input {
            width: 100%;
        }

        .control-group .value-display {
            display: inline-block;
            width: 40px;
            text-align: right;
            margin-left: 10px;
        }

        #simulation-container {
            flex: 1;
            display: flex;
            justify-content: center;
            align-items: center;
            position: relative;
        }

        canvas {
            background-color: #1a1a1a;
            box-shadow: 0 0 20px rgba(33, 150, 243, 0.3);
        }
    </style>
</head>
<body>
    <div class="control-panel">
        <h1>六边形弹球模拟器</h1>
        <div class="control-group">
            <label>
                小球大小: <span id="ball-size-value" class="value-display">15</span>px
                <input type="range" id="ball-size" min="5" max="30" value="15">
            </label>
        </div>
        <div class="control-group">
            <label>
                重力: <span id="gravity-value" class="value-display">0.50</span>
                <input type="range" id="gravity" min="0" max="100" value="50">
            </label>
        </div>
        <div class="control-group">
            <label>
                弹性: <span id="elasticity-value" class="value-display">0.90</span>
                <input type="range" id="elasticity" min="0" max="100" value="90">
            </label>
        </div>
        <div class="control-group">
            <label>
                旋转速度: <span id="rotation-value" class="value-display">1.0</span>°/帧
                <input type="range" id="rotation" min="0" max="50" value="10">
            </label>
        </div>
    </div>
    
    <div id="simulation-container">
        <canvas id="gameCanvas" width="800" height="600"></canvas>
    </div>

    <script>
        // 获取canvas和上下文
        const canvas = document.getElementById('gameCanvas');
        const ctx = canvas.getContext('2d');
        
        // 获取控制元素
        const ballSizeSlider = document.getElementById('ball-size');
        const gravitySlider = document.getElementById('gravity');
        const elasticitySlider = document.getElementById('elasticity');
        const rotationSlider = document.getElementById('rotation');
        
        const ballSizeValue = document.getElementById('ball-size-value');
        const gravityValue = document.getElementById('gravity-value');
        const elasticityValue = document.getElementById('elasticity-value');
        const rotationValue = document.getElementById('rotation-value');
        
        // 初始化参数
        let params = {
            ballSize: 15,
            gravity: 0.5,
            elasticity: 0.9,
            rotationSpeed: 1.0
        };
        
        // 小球对象
        let ball = {
            x: canvas.width / 2,
            y: canvas.height / 2,
            radius: params.ballSize,
            vx: 3,
            vy: 2,
            color: '#ff5252', // 初始颜色为红色
            trail: [] // 轨迹点
        };
        
        // 六边形参数
        const hexRadius = Math.min(canvas.width, canvas.height) * 0.4;
        const hexCenterX = canvas.width / 2;
        const hexCenterY = canvas.height / 2;
        let hexRotation = 0; // 六边形旋转角度
        
        // 颜色数组（用于碰撞时切换颜色）
        const colors = ['#ff5252', '#4caf50', '#e91e63', '#ff9800', '#9c27b0'];
        let colorIndex = 0;
        
        // 更新显示值
        function updateDisplayValues() {
            ballSizeValue.textContent = params.ballSize;
            gravityValue.textContent = params.gravity.toFixed(2);
            elasticityValue.textContent = params.elasticity.toFixed(2);
            rotationValue.textContent = params.rotationSpeed.toFixed(1);
        }
        
        // 添加事件监听器到滑块
        ballSizeSlider.addEventListener('input', function() {
            params.ballSize = parseInt(this.value);
            ball.radius = params.ballSize;
            updateDisplayValues();
        });
        
        gravitySlider.addEventListener('input', function() {
            params.gravity = parseFloat(this.value) / 100;
            updateDisplayValues();
        });
        
        elasticitySlider.addEventListener('input', function() {
            params.elasticity = parseFloat(this.value) / 100;
            updateDisplayValues();
        });
        
        rotationSlider.addEventListener('input', function() {
            params.rotationSpeed = parseFloat(this.value) / 10;
            updateDisplayValues();
        });
        
        // 计算六边形顶点
        function getHexagonVertices() {
            const vertices = [];
            for (let i = 0; i < 6; i++) {
                const angle = (Math.PI / 3) * i + hexRotation;
                vertices.push({
                    x: hexCenterX + hexRadius * Math.cos(angle),
                    y: hexCenterY + hexRadius * Math.sin(angle)
                });
            }
            return vertices;
        }
        
        // 检查点是否在六边形内
        function isPointInHexagon(x, y) {
            const vertices = getHexagonVertices();
            
            // 使用射线投射算法检查点是否在多边形内
            let inside = false;
            for (let i = 0, j = vertices.length - 1; i < vertices.length; j = i++) {
                if (((vertices[i].y > y) !== (vertices[j].y > y)) &&
                    (x < (vertices[j].x - vertices[i].x) * (y - vertices[i].y) / (vertices[j].y - vertices[i].y) + vertices[i].x)) {
                    inside = !inside;
                }
            }
            return inside;
        }
        
        // 获取点到线段的最短距离和法向量
        function pointToLineDistance(px, py, x1, y1, x2, y2) {
            const dx = x2 - x1;
            const dy = y2 - y1;
            const length = Math.sqrt(dx * dx + dy * dy);
            
            if (length === 0) return { distance: 0, nx: 0, ny: 0 }; // 线段长度为0
            
            const nx = -dy / length; // 法向量
            const ny = dx / length;
            
            // 计算点到线段所在直线的距离
            const distance = Math.abs((px - x1) * ny - (py - y1) * nx);
            
            // 计算点在线段上的投影
            const t = ((px - x1) * dx + (py - y1) * dy) / (length * length);
            
            // 如果投影在线段内，则返回点到直线的距离
            if (t >= 0 && t <= 1) {
                return { distance, nx, ny };
            }
            
            // 否则返回到端点的距离
            const dist1 = Math.sqrt((px - x1) * (px - x1) + (py - y1) * (py - y1));
            const dist2 = Math.sqrt((px - x2) * (px - x2) + (py - y2) * (py - y2));
            
            if (dist1 < dist2) {
                return { distance: dist1, nx: (px - x1) / dist1, ny: (py - y1) / dist1 };
            } else {
                return { distance: dist2, nx: (px - x2) / dist2, ny: (py - y2) / dist2 };
            }
        }
        
        // 检查球是否与六边形边界碰撞
        function checkCollision() {
            const vertices = getHexagonVertices();
            
            for (let i = 0; i < vertices.length; i++) {
                const nextIndex = (i + 1) % vertices.length;
                const x1 = vertices[i].x;
                const y1 = vertices[i].y;
                const x2 = vertices[nextIndex].x;
                const y2 = vertices[nextIndex].y;
                
                const result = pointToLineDistance(ball.x, ball.y, x1, y1, x2, y2);
                
                // 如果球与线段的距离小于球的半径，则发生碰撞
                if (result.distance < ball.radius) {
                    // 改变球的颜色
                    colorIndex = (colorIndex + 1) % colors.length;
                    ball.color = colors[colorIndex];
                    
                    // 计算反射向量
                    const dotProduct = ball.vx * result.nx + ball.vy * result.ny;
                    
                    // 只有当球向墙壁移动时才反弹（避免粘在墙上）
                    if (dotProduct < 0) {
                        ball.vx = ball.vx - 2 * dotProduct * result.nx;
                        ball.vy = ball.vy - 2 * dotProduct * result.ny;
                        
                        // 应用弹性系数
                        ball.vx *= params.elasticity;
                        ball.vy *= params.elasticity;
                        
                        // 将球移出墙壁以避免粘连
                        ball.x = ball.x + result.nx * (ball.radius - result.distance);
                        ball.y = ball.y + result.ny * (ball.radius - result.distance);
                    }
                }
            }
        }
        
        // 绘制六边形
        function drawHexagon() {
            const vertices = getHexagonVertices();
            
            ctx.beginPath();
            ctx.moveTo(vertices[0].x, vertices[0].y);
            for (let i = 1; i < vertices.length; i++) {
                ctx.lineTo(vertices[i].x, vertices[i].y);
            }
            ctx.closePath();
            
            // 绘制发光效果
            ctx.shadowColor = '#64b5f6';
            ctx.shadowBlur = 15;
            ctx.strokeStyle = '#64b5f6';
            ctx.lineWidth = 3;
            ctx.stroke();
            
            // 重置阴影以避免影响其他元素
            ctx.shadowBlur = 0;
        }
        
        // 绘制球
        function drawBall() {
            // 添加当前球位置到轨迹
            ball.trail.push({x: ball.x, y: ball.y, radius: ball.radius});
            
            // 限制轨迹长度以提高性能
            if (ball.trail.length > 20) {
                ball.trail.shift();
            }
            
            // 绘制轨迹（从最旧到最新）
            for (let i = 0; i < ball.trail.length; i++) {
                const point = ball.trail[i];
                const alpha = i / ball.trail.length * 0.5; // 透明度逐渐增加
                
                ctx.beginPath();
                ctx.arc(point.x, point.y, point.radius, 0, Math.PI * 2);
                ctx.fillStyle = ball.color.replace(')', `, ${alpha})`).replace('rgb', 'rgba');
                ctx.fill();
            }
            
            // 绘制球本身
            ctx.beginPath();
            ctx.arc(ball.x, ball.y, ball.radius, 0, Math.PI * 2);
            ctx.fillStyle = ball.color;
            ctx.fill();
        }
        
        // 更新球的位置
        function updateBall() {
            // 应用重力
            ball.vy += params.gravity;
            
            // 更新位置
            ball.x += ball.vx;
            ball.y += ball.vy;
            
            // 检查碰撞
            checkCollision();
        }
        
        // 主动画循环
        function animate() {
            // 清除画布
            ctx.clearRect(0, 0, canvas.width, canvas.height);
            
            // 更新六边形旋转
            hexRotation += params.rotationSpeed * Math.PI / 180;
            
            // 更新球
            updateBall();
            
            // 绘制六边形
            drawHexagon();
            
            // 绘制球
            drawBall();
            
            // 继续动画循环
            requestAnimationFrame(animate);
        }
        
        // 初始化显示值
        updateDisplayValues();
        
        // 开始动画
        animate();
    </script>
</body>
</html>